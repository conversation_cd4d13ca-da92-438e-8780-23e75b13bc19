// إدارة البيانات والتخزين المحلي

// متغيرات البيانات الرئيسية
let storeItems = JSON.parse(localStorage.getItem('storeItems')) || [];
let categories = JSON.parse(localStorage.getItem('categories')) || [];
let transactions = JSON.parse(localStorage.getItem('transactions')) || [];
let users = JSON.parse(localStorage.getItem('users')) || [
  { id: 1, username: 'admin', password: 'admin123', role: 'admin', active: true, lastLogin: null }
];
let currentUser = JSON.parse(localStorage.getItem('currentUser')) || null;
let settings = JSON.parse(localStorage.getItem('settings')) || {
  lowStockThreshold: 10,
  expiryWarningDays: 30,
  autoBackupFrequency: 'weekly',
  enableNotifications: true
};

// حفظ البيانات في التخزين المحلي
function saveData() {
  localStorage.setItem('storeItems', JSON.stringify(storeItems));
  localStorage.setItem('categories', JSON.stringify(categories));
  localStorage.setItem('transactions', JSON.stringify(transactions));
  localStorage.setItem('users', JSON.stringify(users));
  localStorage.setItem('currentUser', JSON.stringify(currentUser));
  localStorage.setItem('settings', JSON.stringify(settings));
}

// تصدير البيانات
async function exportData() {
  try {
    const data = {
      storeItems: storeItems,
      categories: categories,
      transactions: transactions,
      users: users,
      settings: settings
    };
    
    const jsonString = JSON.stringify(data, null, 2);
    
    if ('showSaveFilePicker' in window) {
      const fileHandle = await window.showSaveFilePicker({
        suggestedName: 'store-data.json',
        types: [{
          description: 'JSON Files',
          accept: {'application/json': ['.json']},
        }],
      });
      
      const writable = await fileHandle.createWritable();
      await writable.write(jsonString);
      await writable.close();
      
      showNotification('تم تصدير البيانات بنجاح!', 'success');
    } else {
      const blob = new Blob([jsonString], {type: 'application/json'});
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'store-data.json';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      showNotification('تم تصدير البيانات بنجاح!', 'success');
    }
  } catch (error) {
    console.error('Error exporting data:', error);
    showNotification('خطأ في تصدير البيانات: ' + error.message, 'error');
  }
}

// استيراد البيانات
async function importData() {
  try {
    let fileContent;
    
    if ('showOpenFilePicker' in window) {
      const [fileHandle] = await window.showOpenFilePicker({
        types: [{
          description: 'JSON Files',
          accept: {'application/json': ['.json']},
        }],
        multiple: false,
      });
      
      const file = await fileHandle.getFile();
      fileContent = await file.text();
    } else {
      return new Promise((resolve) => {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        
        input.onchange = (event) => {
          const file = event.target.files[0];
          if (!file) {
            showNotification('لم يتم اختيار ملف', 'warning');
            resolve();
            return;
          }
          
          const reader = new FileReader();
          reader.onload = (e) => {
            fileContent = e.target.result;
            processImportedData(fileContent);
            resolve();
          };
          reader.readAsText(file);
        };
        
        input.click();
      });
    }
    
    if (fileContent) {
      processImportedData(fileContent);
    }
  } catch (error) {
    console.error('Error importing data:', error);
    showNotification('خطأ في استيراد البيانات: ' + error.message, 'error');
  }
}

// معالجة البيانات المستوردة
function processImportedData(fileContent) {
  try {
    const data = JSON.parse(fileContent);
    
    if (!data.storeItems || !data.categories || !data.transactions) {
      throw new Error('تنسيق البيانات غير صحيح');
    }
    
    storeItems = data.storeItems;
    categories = data.categories;
    transactions = data.transactions;
    
    if (data.users) users = data.users;
    if (data.settings) settings = data.settings;
    
    saveData();
    showNotification('تم استيراد البيانات بنجاح!', 'success');
    
    // تحديث العرض الحالي
    refreshCurrentView();
  } catch (error) {
    console.error('Error processing imported data:', error);
    showNotification('خطأ في معالجة البيانات المستوردة: ' + error.message, 'error');
  }
}

// إعادة تعيين البيانات
function resetData() {
  if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
    storeItems = [];
    categories = [];
    transactions = [];
    
    saveData();
    showNotification('تم إعادة تعيين البيانات بنجاح', 'success');
    refreshCurrentView();
  }
}

// إنشاء نسخة احتياطية
function createBackup() {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupName = `backup-${timestamp}`;
  
  const data = {
    storeItems: storeItems,
    categories: categories,
    transactions: transactions,
    users: users,
    settings: settings,
    timestamp: new Date().toISOString()
  };
  
  const jsonString = JSON.stringify(data, null, 2);
  const blob = new Blob([jsonString], {type: 'application/json'});
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `${backupName}.json`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
  
  showNotification('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
}

// تحديث العرض الحالي
function refreshCurrentView() {
  const activeTab = document.querySelector('.nav-link.active');
  if (activeTab) {
    const tabId = activeTab.getAttribute('href').substring(1);
    switch(tabId) {
      case 'dashboard':
        updateDashboard();
        break;
      case 'store':
        loadStoreItems();
        break;
      case 'categories':
        loadCategoriesTable();
        break;
      case 'intoStore':
        loadCategories();
        loadRecentTransactions('in');
        break;
      case 'outOfStore':
        loadRecentTransactions('out');
        break;
      case 'reports':
        loadReportCategories();
        break;
    }
  }
}
