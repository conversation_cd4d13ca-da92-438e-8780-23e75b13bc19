<!-- النوافذ المنبثقة -->

<!-- <PERSON>gin Modal -->
<div class="modal fade" id="loginModal" tabindex="-1" aria-labelledby="loginModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="loginModalLabel">تسجيل الدخول</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="loginForm">
          <div class="mb-3">
            <label for="username" class="form-label">اسم المستخدم</label>
            <input type="text" class="form-control" id="username" required>
          </div>
          <div class="mb-3">
            <label for="password" class="form-label">كلمة المرور</label>
            <input type="password" class="form-control" id="password" required>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
        <button type="button" class="btn btn-primary" id="loginSubmitBtn">تسجيل الدخول</button>
      </div>
    </div>
  </div>
</div>

<!-- User Management Modal -->
<div class="modal fade" id="userManagementModal" tabindex="-1" aria-labelledby="userManagementModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="userManagementModalLabel">إدارة المستخدمين</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <button class="btn btn-primary mb-3" id="addUserBtn">إضافة مستخدم جديد</button>
        <div class="table-container">
          <table class="table table-bordered table-striped" id="usersTable">
            <thead class="table-dark">
              <tr>
                <th>اسم المستخدم</th>
                <th>الدور</th>
                <th>آخر تسجيل دخول</th>
                <th>الحالة</th>
                <th>الإجراءات</th>
              </tr>
            </thead>
            <tbody></tbody>
          </table>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
      </div>
    </div>
  </div>
</div>

<!-- Add/Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="editUserModalLabel">إضافة مستخدم جديد</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="editUserForm">
          <input type="hidden" id="editUserId">
          <div class="mb-3">
            <label for="editUsername" class="form-label">اسم المستخدم</label>
            <input type="text" class="form-control" id="editUsername" required>
          </div>
          <div class="mb-3">
            <label for="editPassword" class="form-label">كلمة المرور</label>
            <input type="password" class="form-control" id="editPassword">
            <small class="form-text text-muted">اتركه فارغًا إذا كنت لا ترغب في تغيير كلمة المرور</small>
          </div>
          <div class="mb-3">
            <label for="editRole" class="form-label">الدور</label>
            <select class="form-select" id="editRole" required>
              <option value="admin">مدير</option>
              <option value="manager">مشرف</option>
              <option value="user">مستخدم</option>
              <option value="viewer">مراقب</option>
            </select>
          </div>
          <div class="mb-3">
            <div class="form-check form-switch">
              <input class="form-check-input" type="checkbox" id="editActive" checked>
              <label class="form-check-label" for="editActive">نشط</label>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
        <button type="button" class="btn btn-primary" id="saveUserBtn">حفظ</button>
      </div>
    </div>
  </div>
</div>

<!-- Add/Edit Category Modal -->
<div class="modal fade" id="categoryModal" tabindex="-1" aria-labelledby="categoryModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="categoryModalLabel">إضافة فئة جديدة</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="categoryForm">
          <input type="hidden" id="categoryId">
          <div class="mb-3">
            <label for="categoryName" class="form-label">اسم الفئة</label>
            <input type="text" class="form-control" id="categoryName" required>
          </div>
          <div class="mb-3">
            <label for="categoryDescription" class="form-label">الوصف</label>
            <textarea class="form-control" id="categoryDescription" rows="3"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
        <button type="button" class="btn btn-primary" id="saveCategoryBtn">حفظ</button>
      </div>
    </div>
  </div>
</div>

<!-- Schedule Report Modal -->
<div class="modal fade" id="scheduleReportModal" tabindex="-1" aria-labelledby="scheduleReportModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="scheduleReportModalLabel">جدولة التقرير</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="scheduleReportForm">
          <div class="mb-3">
            <label for="scheduleFrequency" class="form-label">التكرار</label>
            <select class="form-select" id="scheduleFrequency" required>
              <option value="daily">يومي</option>
              <option value="weekly">أسبوعي</option>
              <option value="monthly">شهري</option>
            </select>
          </div>
          <div class="mb-3">
            <label for="scheduleEmail" class="form-label">البريد الإلكتروني</label>
            <input type="email" class="form-control" id="scheduleEmail" placeholder="أدخل البريد الإلكتروني لاستلام التقرير" required>
          </div>
          <div class="mb-3">
            <label for="scheduleFormat" class="form-label">تنسيق التقرير</label>
            <select class="form-select" id="scheduleFormat" required>
              <option value="pdf">PDF</option>
              <option value="excel">Excel</option>
              <option value="csv">CSV</option>
            </select>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
        <button type="button" class="btn btn-primary" id="saveScheduleBtn">حفظ</button>
      </div>
    </div>
  </div>
</div>

<!-- Password Modal -->
<div class="modal fade" id="passwordModal" tabindex="-1" aria-labelledby="passwordModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="passwordModalLabel">كلمة المرور مطلوبة</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="passwordForm">
          <div class="mb-3">
            <label for="adminPassword" class="form-label">أدخل كلمة مرور المدير</label>
            <input type="password" class="form-control" id="adminPassword" required>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
        <button type="button" class="btn btn-primary" id="submitPassword">إرسال</button>
      </div>
    </div>
  </div>
</div>

<!-- Barcode Scanner Modal -->
<div class="modal fade" id="barcodeScannerModal" tabindex="-1" aria-labelledby="barcodeScannerModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="barcodeScannerModalLabel">مسح الباركود</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div id="scanner-container" class="text-center">
          <video id="scanner" class="w-100"></video>
        </div>
        <div class="mt-3 text-center">
          <p>قم بتوجيه الكاميرا نحو الباركود</p>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
      </div>
    </div>
  </div>
</div>

<!-- Item Details Modal -->
<div class="modal fade" id="itemDetailsModal" tabindex="-1" aria-labelledby="itemDetailsModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="itemDetailsModalLabel">تفاصيل العنصر</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="row mb-4">
          <div class="col-md-6">
            <h6>معلومات العنصر</h6>
            <table class="table table-bordered">
              <tr>
                <th>الباركود</th>
                <td id="detailBarcode"></td>
              </tr>
              <tr>
                <th>الفئة</th>
                <td id="detailCategory"></td>
              </tr>
              <tr>
                <th>الرصيد الحالي</th>
                <td id="detailBalance"></td>
              </tr>
              <tr>
                <th>تاريخ انتهاء الصلاحية</th>
                <td id="detailExpiry"></td>
              </tr>
              <tr>
                <th>آخر تحديث</th>
                <td id="detailLastUpdated"></td>
              </tr>
            </table>
          </div>
          <div class="col-md-6">
            <div class="chart-container">
              <canvas id="itemHistoryChart"></canvas>
            </div>
          </div>
        </div>
        <h6>سجل العمليات</h6>
        <div class="table-container">
          <table class="table table-bordered table-striped" id="itemTransactionsTable">
            <thead class="table-dark">
              <tr>
                <th>النوع</th>
                <th>الكمية</th>
                <th>التاريخ</th>
                <th>الوقت</th>
                <th>الرصيد المتبقي</th>
                <th>المستخدم</th>
              </tr>
            </thead>
            <tbody></tbody>
          </table>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
        <button type="button" class="btn btn-primary" id="printItemDetailsBtn">طباعة</button>
      </div>
    </div>
  </div>
</div>
