<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>نظام إدارة المخزون</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.css">
  <style>
    body {
      background-color: #f8f9fa;
      transition: background-color 0.3s, color 0.3s;
    }
    
    .dark-mode {
      background-color: #212529;
      color: #f8f9fa;
    }
    
    .dark-mode .main-container {
      background-color: #343a40;
      color: #f8f9fa;
    }
    
    .dark-mode .table {
      color: #f8f9fa;
    }
    
    .dark-mode .form-control,
    .dark-mode .form-select {
      background-color: #495057;
      color: #f8f9fa;
      border-color: #6c757d;
    }
    
    .main-container {
      max-width: 1200px;
      margin: 50px auto;
      padding: 20px;
      background-color: #fff;
      border-radius: 10px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      transition: background-color 0.3s, color 0.3s;
    }
    
    .form-container {
      display: none;
      margin-top: 20px;
    }
    
    .table-container {
      margin-top: 20px;
      overflow-x: auto;
    }
    
    .selected {
      background-color: #e9ecef;
    }
    
    .dark-mode .selected {
      background-color: #495057;
    }
    
    .dashboard-cards {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 20px;
      margin-bottom: 20px;
    }
    
    .dashboard-card {
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s;
    }
    
    .dashboard-card:hover {
      transform: translateY(-5px);
    }
    
    .card-blue {
      background-color: #cfe2ff;
      border-left: 5px solid #0d6efd;
    }
    
    .card-green {
      background-color: #d1e7dd;
      border-left: 5px solid #198754;
    }
    
    .card-yellow {
      background-color: #fff3cd;
      border-left: 5px solid #ffc107;
    }
    
    .card-red {
      background-color: #f8d7da;
      border-left: 5px solid #dc3545;
    }
    
    .dark-mode .card-blue {
      background-color: #0d6efd33;
      border-left: 5px solid #0d6efd;
    }
    
    .dark-mode .card-green {
      background-color: #19875433;
      border-left: 5px solid #198754;
    }
    
    .dark-mode .card-yellow {
      background-color: #ffc10733;
      border-left: 5px solid #ffc107;
    }
    
    .dark-mode .card-red {
      background-color: #dc354533;
      border-left: 5px solid #dc3545;
    }
    
    .search-container {
      margin-bottom: 20px;
    }
    
    .chart-container {
      height: 300px;
      margin-bottom: 20px;
    }
    
    .notification-badge {
      position: absolute;
      top: -5px;
      right: -5px;
      background-color: #dc3545;
      color: white;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
    }
    
    .notification-container {
      max-height: 300px;
      overflow-y: auto;
    }
    
    .notification-item {
      padding: 10px;
      border-bottom: 1px solid #dee2e6;
    }
    
    .notification-item:last-child {
      border-bottom: none;
    }
    
    .btn-icon {
      position: relative;
    }
    
    @media (max-width: 768px) {
      .main-container {
        margin: 20px 10px;
        padding: 15px;
      }
      
      .dashboard-cards {
        grid-template-columns: 1fr;
      }
    }
    
    .loading-spinner {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 9999;
      justify-content: center;
      align-items: center;
    }
    
    .spinner-border {
      width: 3rem;
      height: 3rem;
    }
  </style>
</head>
<body>
  <!-- Loading Spinner -->
  <div class="loading-spinner" id="loadingSpinner">
    <div class="spinner-border text-light" role="status">
      <span class="visually-hidden">جاري التحميل...</span>
    </div>
  </div>

  <div class="main-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h1 class="mb-0">نظام إدارة المخزون</h1>
      <div class="d-flex gap-2">
        <button class="btn btn-outline-secondary" id="darkModeToggle" title="تبديل الوضع المظلم">
          <i class="bi bi-moon"></i>
        </button>
        <div class="dropdown">
          <button class="btn btn-outline-secondary btn-icon" type="button" id="notificationsDropdown" data-bs-toggle="dropdown" aria-expanded="false" title="الإشعارات">
            <i class="bi bi-bell"></i>
            <span class="notification-badge" id="notificationBadge" style="display: none;">0</span>
          </button>
          <ul class="dropdown-menu dropdown-menu-end notification-container" aria-labelledby="notificationsDropdown" id="notificationsList">
            <li><div class="notification-item">لا توجد إشعارات</div></li>
          </ul>
        </div>
        <div class="dropdown">
          <button class="btn btn-outline-secondary" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false" title="المستخدم">
            <i class="bi bi-person-circle"></i>
          </button>
          <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
            <li><a class="dropdown-item" href="#" id="loginButton">تسجيل الدخول</a></li>
            <li><a class="dropdown-item" href="#" id="logoutButton" style="display: none;">تسجيل الخروج</a></li>
            <li><a class="dropdown-item" href="#" id="manageUsersButton" style="display: none;">إدارة المستخدمين</a></li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Navigation Tabs -->
    <ul class="nav nav-tabs mb-4" id="mainTabs">
      <li class="nav-item">
        <a class="nav-link active" id="dashboardTab" data-bs-toggle="tab" href="#dashboard">لوحة المعلومات</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" id="storeTab" data-bs-toggle="tab" href="#store">المخزون</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" id="intoStoreTab" data-bs-toggle="tab" href="#intoStore">إضافة للمخزون</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" id="outOfStoreTab" data-bs-toggle="tab" href="#outOfStore">سحب من المخزون</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" id="categoriesTab" data-bs-toggle="tab" href="#categories">الفئات</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" id="reportsTab" data-bs-toggle="tab" href="#reports">التقارير</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" id="settingsTab" data-bs-toggle="tab" href="#settings">الإعدادات</a>
      </li>
    </ul>

    <!-- Tab Content -->
    <div class="tab-content">
      <!-- Dashboard Tab -->
      <div class="tab-pane fade show active" id="dashboard">
        <h2>لوحة المعلومات</h2>
        
        <div class="dashboard-cards">
          <div class="dashboard-card card-blue">
            <h3>إجمالي العناصر</h3>
            <h2 id="totalItems">0</h2>
          </div>
          <div class="dashboard-card card-green">
            <h3>إجمالي الفئات</h3>
            <h2 id="totalCategories">0</h2>
          </div>
          <div class="dashboard-card card-yellow">
            <h3>عمليات اليوم</h3>
            <h2 id="todayTransactions">0</h2>
          </div>
          <div class="dashboard-card card-red">
            <h3>عناصر منخفضة</h3>
            <h2 id="lowStockItems">0</h2>
          </div>
        </div>
        
        <div class="row">
          <div class="col-md-6">
            <div class="card mb-4">
              <div class="card-header">حركة المخزون</div>
              <div class="card-body">
                <div class="chart-container">
                  <canvas id="inventoryMovementChart"></canvas>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="card mb-4">
              <div class="card-header">توزيع الفئات</div>
              <div class="card-body">
                <div class="chart-container">
                  <canvas id="categoriesChart"></canvas>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="card mb-4">
          <div class="card-header">العناصر منخفضة المخزون</div>
          <div class="card-body">
            <div class="table-container">
              <table class="table table-bordered table-striped" id="lowStockTable">
                <thead class="table-dark">
                  <tr>
                    <th>الباركود</th>
                    <th>الفئة</th>
                    <th>الرصيد المتبقي</th>
                    <th>آخر تحديث</th>
                    <th>الإجراءات</th>
                  </tr>
                </thead>
                <tbody></tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- Store Tab -->
      <div class="tab-pane fade" id="store">
        <h2>المخزون</h2>
        
        <div class="search-container">
          <div class="row">
            <div class="col-md-3 mb-2">
              <input type="text" class="form-control" id="searchBarcode" placeholder="بحث بالباركود">
            </div>
            <div class="col-md-3 mb-2">
              <select class="form-select" id="searchCategory">
                <option value="">جميع الفئات</option>
              </select>
            </div>
            <div class="col-md-3 mb-2">
              <input type="date" class="form-control" id="searchDate">
            </div>
            <div class="col-md-3 mb-2">
              <button class="btn btn-primary w-100" id="searchButton">بحث</button>
            </div>
          </div>
        </div>
        
        <div class="table-container">
          <table class="table table-bordered table-striped" id="storeTable">
            <thead class="table-dark">
              <tr>
                <th>الباركود</th>
                <th>الفئة</th>
                <th>الرصيد المتبقي</th>
                <th>تاريخ انتهاء الصلاحية</th>
                <th>آخر تحديث</th>
                <th>الإجراءات</th>
              </tr>
            </thead>
            <tbody></tbody>
          </table>
        </div>
        <div class="mt-3">
          <button class="btn btn-success" id="exportStoreBtn">تصدير</button>
          <button class="btn btn-primary" id="printStoreBtn">طباعة</button>
        </div>
      </div>

      <!-- Into Store Tab -->
      <div class="tab-pane fade" id="intoStore">
        <h2>إضافة للمخزون</h2>
        <form id="intoStoreForm">
          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="barcode" class="form-label">الباركود</label>
              <div class="input-group">
                <input type="text" class="form-control" id="barcode" placeholder="أدخل الباركود" required>
                <button class="btn btn-outline-secondary" type="button" id="scanBarcodeBtn" title="مسح الباركود">
                  <i class="bi bi-upc-scan"></i>
                </button>
              </div>
            </div>
            <div class="col-md-6 mb-3">
              <label for="category" class="form-label">الفئة</label>
              <select class="form-select" id="category" required>
                <option value="">اختر الفئة</option>
              </select>
            </div>
          </div>
          <div class="row">
            <div class="col-md-4 mb-3">
              <label for="number" class="form-label">الكمية</label>
              <input type="number" class="form-control" id="number" placeholder="أدخل الكمية" required>
            </div>
            <div class="col-md-4 mb-3">
              <label for="date" class="form-label">تاريخ الإضافة</label>
              <input type="date" class="form-control" id="date" required>
            </div>
            <div class="col-md-4 mb-3">
              <label for="expiryDate" class="form-label">تاريخ انتهاء الصلاحية</label>
              <input type="date" class="form-control" id="expiryDate">
            </div>
          </div>
          <button type="submit" class="btn btn-success">حفظ</button>
          <button type="reset" class="btn btn-secondary">إعادة تعيين</button>
        </form>
        
        <div class="mt-4">
          <h3>آخر العمليات</h3>
          <div class="table-container">
            <table class="table table-bordered table-striped" id="recentIntoStoreTable">
              <thead class="table-dark">
                <tr>
                  <th>الباركود</th>
                  <th>الفئة</th>
                  <th>الكمية</th>
                  <th>التاريخ</th>
                  <th>المستخدم</th>
                </tr>
              </thead>
              <tbody></tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Out Of Store Tab -->
      <div class="tab-pane fade" id="outOfStore">
        <h2>سحب من المخزون</h2>
        <form id="outOfStoreForm">
          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="outBarcode" class="form-label">الباركود</label>
              <div class="input-group">
                <input type="text" class="form-control" id="outBarcode" placeholder="أدخل الباركود" oninput="autoFillCategory()" required>
                <button class="btn btn-outline-secondary" type="button" id="outScanBarcodeBtn" title="مسح الباركود">
                  <i class="bi bi-upc-scan"></i>
                </button>
              </div>
            </div>
            <div class="col-md-6 mb-3">
              <label for="outCategory" class="form-label">الفئة</label>
              <input type="text" class="form-control" id="outCategory" placeholder="الفئة" readonly>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="outNumber" class="form-label">الكمية</label>
              <input type="number" class="form-control" id="outNumber" placeholder="أدخل الكمية" required>
            </div>
            <div class="col-md-6 mb-3">
              <label for="outDate" class="form-label">تاريخ السحب</label>
              <input type="date" class="form-control" id="outDate" required>
            </div>
          </div>
          <button type="submit" class="btn btn-warning">حفظ</button>
          <button type="reset" class="btn btn-secondary">إعادة تعيين</button>
        </form>
        
        <div class="mt-4">
          <h3>آخر العمليات</h3>
          <div class="table-container">
            <table class="table table-bordered table-striped" id="recentOutOfStoreTable">
              <thead class="table-dark">
                <tr>
                  <th>الباركود</th>
                  <th>الفئة</th>
                  <th>الكمية</th>
                  <th>التاريخ</th>
                  <th>المستخدم</th>
                </tr>
              </thead>
              <tbody></tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Categories Tab -->
      <div class="tab-pane fade" id="categories">
        <h2>الفئات</h2>
        <div class="mb-3">
          <button class="btn btn-primary" id="addCategoryBtn">إضافة فئة جديدة</button>
        </div>
        <div class="table-container">
          <table class="table table-bordered table-striped" id="categoriesTable">
            <thead class="table-dark">
              <tr>
                <th>الفئة</th>
                <th>عدد العناصر</th>
                <th>الإجراءات</th>
              </tr>
            </thead>
            <tbody></tbody>
          </table>
        </div>
      </div>

      <!-- Reports Tab -->
      <div class="tab-pane fade" id="reports">
        <h2>التقارير</h2>
        
        <div class="card mb-4">
          <div class="card-header">إنشاء تقرير</div>
          <div class="card-body">
            <form id="reportForm">
              <div class="row">
                <div class="col-md-4 mb-3">
                  <label for="reportType" class="form-label">نوع التقرير</label>
                  <select class="form-select" id="reportType" required>
                    <option value="">اختر نوع التقرير</option>
                    <option value="monthly">تقرير شهري</option>
                    <option value="category">تقرير حسب الفئة</option>
                    <option value="item">تقرير حسب العنصر</option>
                    <option value="expiry">تقرير انتهاء الصلاحية</option>
                  </select>
                </div>
                <div class="col-md-4 mb-3">
                  <label for="reportStartDate" class="form-label">من تاريخ</label>
                  <input type="date" class="form-control" id="reportStartDate" required>
                </div>
                <div class="col-md-4 mb-3">
                  <label for="reportEndDate" class="form-label">إلى تاريخ</label>
                  <input type="date" class="form-control" id="reportEndDate" required>
                </div>
              </div>
              <div class="row">
                <div class="col-md-4 mb-3">
                  <label for="reportCategory" class="form-label">الفئة</label>
                  <select class="form-select" id="reportCategory">
                    <option value="">جميع الفئات</option>
                  </select>
                </div>
                <div class="col-md-4 mb-3">
                  <label for="reportFormat" class="form-label">تنسيق التقرير</label>
                  <select class="form-select" id="reportFormat" required>
                    <option value="table">جدول</option>
                    <option value="chart">رسم بياني</option>
                    <option value="pdf">PDF</option>
                    <option value="excel">Excel</option>
                    <option value="csv">CSV</option>
                  </select>
                </div>
                <div class="col-md-4 mb-3 d-flex align-items-end">
                  <button type="submit" class="btn btn-primary w-100">إنشاء التقرير</button>
                </div>
              </div>
            </form>
          </div>
        </div>
        
        <div class="card">
          <div class="card-header">نتائج التقرير</div>
          <div class="card-body">
            <div id="reportChartContainer" class="chart-container" style="display: none;">
              <canvas id="reportChart"></canvas>
            </div>
            <div class="table-container">
              <table class="table table-bordered table-striped" id="reportTable">
                <thead class="table-dark">
                  <tr>
                    <th>الشهر</th>
                    <th>الفئة</th>
                    <th>الرصيد المضاف</th>
                    <th>الرصيد المسحوب</th>
                    <th>الرصيد المتبقي</th>
                    <th>الرصيد السابق</th>
                    <th>الإجمالي</th>
                  </tr>
                </thead>
                <tbody></tbody>
              </table>
            </div>
            <div class="mt-3">
              <button class="btn btn-success" id="exportReportBtn">تصدير</button>
              <button class="btn btn-primary" id="printReportBtn">طباعة</button>
              <button class="btn btn-info" id="scheduleReportBtn">جدولة التقرير</button>
            </div>
          </div>
        </div>
      </div>

      <!-- Settings Tab -->
      <div class="tab-pane fade" id="settings">
        <h2>الإعدادات</h2>
        
        <div class="card mb-4">
          <div class="card-header">إعدادات النظام</div>
          <div class="card-body">
            <form id="settingsForm">
              <div class="mb-3">
                <label for="lowStockThreshold" class="form-label">حد الإنذار للمخزون المنخفض</label>
                <input type="number" class="form-control" id="lowStockThreshold" min="1" value="10">
              </div>
              <div class="mb-3">
                <label for="expiryWarningDays" class="form-label">أيام التنبيه قبل انتهاء الصلاحية</label>
                <input type="number" class="form-control" id="expiryWarningDays" min="1" value="30">
              </div>
              <div class="mb-3">
                <label for="autoBackupFrequency" class="form-label">تكرار النسخ الاحتياطي التلقائي</label>
                <select class="form-select" id="autoBackupFrequency">
                  <option value="daily">يومي</option>
                  <option value="weekly" selected>أسبوعي</option>
                  <option value="monthly">شهري</option>
                  <option value="never">لا يوجد</option>
                </select>
              </div>
              <div class="mb-3">
                <div class="form-check form-switch">
                  <input class="form-check-input" type="checkbox" id="enableNotifications" checked>
                  <label class="form-check-label" for="enableNotifications">تفعيل الإشعارات</label>
                </div>
              </div>
              <button type="submit" class="btn btn-primary">حفظ الإعدادات</button>
            </form>
          </div>
        </div>
        
        <div class="card mb-4">
          <div class="card-header">النسخ الاحتياطي واستعادة البيانات</div>
          <div class="card-body">
            <div class="d-flex gap-2 mb-3">
              <button class="btn btn-primary" id="backupDataBtn">إنشاء نسخة احتياطية</button>
              <button class="btn btn-warning" id="restoreDataBtn">استعادة البيانات</button>
            </div>
            <div class="table-container">
              <table class="table table-bordered table-striped" id="backupsTable">
                <thead class="table-dark">
                  <tr>
                    <th>اسم النسخة</th>
                    <th>التاريخ</th>
                    <th>الحجم</th>
                    <th>الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td colspan="4" class="text-center">لا توجد نسخ احتياطية</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        
        <div class="card">
          <div class="card-header">إعادة تعيين البيانات</div>
          <div class="card-body">
            <div class="alert alert-danger">
              <strong>تحذير!</strong> هذه العمليات لا يمكن التراجع عنها. يرجى التأكد من إنشاء نسخة احتياطية قبل المتابعة.
            </div>
            <div class="d-flex gap-2">
              <button class="btn btn-danger" id="resetDataBtn">إعادة تعيين جميع البيانات</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Modals -->
  <!-- Login Modal -->
  <div class="modal fade" id="loginModal" tabindex="-1" aria-labelledby="loginModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="loginModalLabel">تسجيل الدخول</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="loginForm">
            <div class="mb-3">
              <label for="username" class="form-label">اسم المستخدم</label>
              <input type="text" class="form-control" id="username" required>
            </div>
            <div class="mb-3">
              <label for="password" class="form-label">كلمة المرور</label>
              <input type="password" class="form-control" id="password" required>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
          <button type="button" class="btn btn-primary" id="loginSubmitBtn">تسجيل الدخول</button>
        </div>
      </div>
    </div>
  </div>

  <!-- User Management Modal -->
  <div class="modal fade" id="userManagementModal" tabindex="-1" aria-labelledby="userManagementModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="userManagementModalLabel">إدارة المستخدمين</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <button class="btn btn-primary mb-3" id="addUserBtn">إضافة مستخدم جديد</button>
          <div class="table-container">
            <table class="table table-bordered table-striped" id="usersTable">
              <thead class="table-dark">
                <tr>
                  <th>اسم المستخدم</th>
                  <th>الدور</th>
                  <th>آخر تسجيل دخول</th>
                  <th>الحالة</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody></tbody>
            </table>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Add/Edit User Modal -->
  <div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="editUserModalLabel">إضافة مستخدم جديد</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="editUserForm">
            <input type="hidden" id="editUserId">
            <div class="mb-3">
              <label for="editUsername" class="form-label">اسم المستخدم</label>
              <input type="text" class="form-control" id="editUsername" required>
            </div>
            <div class="mb-3">
              <label for="editPassword" class="form-label">كلمة المرور</label>
              <input type="password" class="form-control" id="editPassword">
              <small class="form-text text-muted">اتركه فارغًا إذا كنت لا ترغب في تغيير كلمة المرور</small>
            </div>
            <div class="mb-3">
              <label for="editRole" class="form-label">الدور</label>
              <select class="form-select" id="editRole" required>
                <option value="admin">مدير</option>
                <option value="manager">مشرف</option>
                <option value="user">مستخدم</option>
                <option value="viewer">مراقب</option>
              </select>
            </div>
            <div class="mb-3">
              <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="editActive" checked>
                <label class="form-check-label" for="editActive">نشط</label>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
          <button type="button" class="btn btn-primary" id="saveUserBtn">حفظ</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Add/Edit Category Modal -->
  <div class="modal fade" id="categoryModal" tabindex="-1" aria-labelledby="categoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="categoryModalLabel">إضافة فئة جديدة</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="categoryForm">
            <input type="hidden" id="categoryId">
            <div class="mb-3">
              <label for="categoryName" class="form-label">اسم الفئة</label>
              <input type="text" class="form-control" id="categoryName" required>
            </div>
            <div class="mb-3">
              <label for="categoryDescription" class="form-label">الوصف</label>
              <textarea class="form-control" id="categoryDescription" rows="3"></textarea>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
          <button type="button" class="btn btn-primary" id="saveCategoryBtn">حفظ</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Schedule Report Modal -->
  <div class="modal fade" id="scheduleReportModal" tabindex="-1" aria-labelledby="scheduleReportModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="scheduleReportModalLabel">جدولة التقرير</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="scheduleReportForm">
            <div class="mb-3">
              <label for="scheduleFrequency" class="form-label">التكرار</label>
              <select class="form-select" id="scheduleFrequency" required>
                <option value="daily">يومي</option>
                <option value="weekly">أسبوعي</option>
                <option value="monthly">شهري</option>
              </select>
            </div>
            <div class="mb-3">
              <label for="scheduleEmail" class="form-label">البريد الإلكتروني</label>
              <input type="email" class="form-control" id="scheduleEmail" placeholder="أدخل البريد الإلكتروني لاستلام التقرير" required>
            </div>
            <div class="mb-3">
              <label for="scheduleFormat" class="form-label">تنسيق التقرير</label>
              <select class="form-select" id="scheduleFormat" required>
                <option value="pdf">PDF</option>
                <option value="excel">Excel</option>
                <option value="csv">CSV</option>
              </select>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
          <button type="button" class="btn btn-primary" id="saveScheduleBtn">حفظ</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Password Modal -->
  <div class="modal fade" id="passwordModal" tabindex="-1" aria-labelledby="passwordModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="passwordModalLabel">كلمة المرور مطلوبة</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="passwordForm">
            <div class="mb-3">
              <label for="adminPassword" class="form-label">أدخل كلمة مرور المدير</label>
              <input type="password" class="form-control" id="adminPassword" required>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
          <button type="button" class="btn btn-primary" id="submitPassword">إرسال</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Barcode Scanner Modal -->
  <div class="modal fade" id="barcodeScannerModal" tabindex="-1" aria-labelledby="barcodeScannerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="barcodeScannerModalLabel">مسح الباركود</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div id="scanner-container" class="text-center">
            <video id="scanner" class="w-100"></video>
          </div>
          <div class="mt-3 text-center">
            <p>قم بتوجيه الكاميرا نحو الباركود</p>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Item Details Modal -->
  <div class="modal fade" id="itemDetailsModal" tabindex="-1" aria-labelledby="itemDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="itemDetailsModalLabel">تفاصيل العنصر</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="row mb-4">
            <div class="col-md-6">
              <h6>معلومات العنصر</h6>
              <table class="table table-bordered">
                <tr>
                  <th>الباركود</th>
                  <td id="detailBarcode"></td>
                </tr>
                <tr>
                  <th>الفئة</th>
                  <td id="detailCategory"></td>
                </tr>
                <tr>
                  <th>الرصيد الحالي</th>
                  <td id="detailBalance"></td>
                </tr>
                <tr>
                  <th>تاريخ انتهاء الصلاحية</th>
                  <td id="detailExpiry"></td>
                </tr>
                <tr>
                  <th>آخر تحديث</th>
                  <td id="detailLastUpdated"></td>
                </tr>
              </table>
            </div>
            <div class="col-md-6">
              <div class="chart-container">
                <canvas id="itemHistoryChart"></canvas>
              </div>
            </div>
          </div>
          <h6>سجل العمليات</h6>
          <div class="table-container">
            <table class="table table-bordered table-striped" id="itemTransactionsTable">
              <thead class="table-dark">
                <tr>
                  <th>النوع</th>
                  <th>الكمية</th>
                  <th>التاريخ</th>
                  <th>الوقت</th>
                  <th>الرصيد المتبقي</th>
                  <th>المستخدم</th>
                </tr>
              </thead>
              <tbody></tbody>
            </table>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
          <button type="button" class="btn btn-primary" id="printItemDetailsBtn">طباعة</button>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/crypto-js@4.1.1/crypto-js.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/quagga@0.12.1/dist/quagga.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/jspdf@2.5.1/dist/jspdf.umd.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/jspdf-autotable@3.5.25/dist/jspdf.plugin.autotable.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
  <script src="script.js"></script>
</body>
</html>