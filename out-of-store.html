<!-- سحب من المخزون -->
<div class="tab-pane fade" id="outOfStore">
  <h2>سحب من المخزون</h2>
  <form id="outOfStoreForm">
    <div class="row">
      <div class="col-md-6 mb-3">
        <label for="outBarcode" class="form-label">الباركود</label>
        <div class="input-group">
          <input type="text" class="form-control" id="outBarcode" placeholder="أدخل الباركود" oninput="autoFillCategory()" required>
          <button class="btn btn-outline-secondary" type="button" id="outScanBarcodeBtn" title="مسح الباركود">
            <i class="bi bi-upc-scan"></i>
          </button>
        </div>
      </div>
      <div class="col-md-6 mb-3">
        <label for="outCategory" class="form-label">الفئة</label>
        <input type="text" class="form-control" id="outCategory" placeholder="الفئة" readonly>
      </div>
    </div>
    <div class="row">
      <div class="col-md-6 mb-3">
        <label for="outNumber" class="form-label">الكمية</label>
        <input type="number" class="form-control" id="outNumber" placeholder="أدخل الكمية" required>
      </div>
      <div class="col-md-6 mb-3">
        <label for="outDate" class="form-label">تاريخ السحب</label>
        <input type="date" class="form-control" id="outDate" required>
      </div>
    </div>
    <button type="submit" class="btn btn-warning">حفظ</button>
    <button type="reset" class="btn btn-secondary">إعادة تعيين</button>
  </form>
  
  <div class="mt-4">
    <h3>آخر العمليات</h3>
    <div class="table-container">
      <table class="table table-bordered table-striped" id="recentOutOfStoreTable">
        <thead class="table-dark">
          <tr>
            <th>الباركود</th>
            <th>الفئة</th>
            <th>الكمية</th>
            <th>التاريخ</th>
            <th>المستخدم</th>
          </tr>
        </thead>
        <tbody></tbody>
      </table>
    </div>
  </div>
</div>
