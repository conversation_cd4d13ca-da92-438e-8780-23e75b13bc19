/* نظام إدارة المخزون - ملف الأنماط */

/* الأنماط العامة */
body {
  background-color: #f8f9fa;
  transition: background-color 0.3s, color 0.3s;
}

/* الوضع المظلم */
.dark-mode {
  background-color: #212529;
  color: #f8f9fa;
}

.dark-mode .main-container {
  background-color: #343a40;
  color: #f8f9fa;
}

.dark-mode .table {
  color: #f8f9fa;
}

.dark-mode .form-control,
.dark-mode .form-select {
  background-color: #495057;
  color: #f8f9fa;
  border-color: #6c757d;
}

/* الحاوية الرئيسية */
.main-container {
  max-width: 1200px;
  margin: 50px auto;
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  transition: background-color 0.3s, color 0.3s;
}

/* حاوية النماذج */
.form-container {
  display: none;
  margin-top: 20px;
}

/* حاوية الجداول */
.table-container {
  margin-top: 20px;
  overflow-x: auto;
}

/* العناصر المحددة */
.selected {
  background-color: #e9ecef;
}

.dark-mode .selected {
  background-color: #495057;
}

/* بطاقات لوحة المعلومات */
.dashboard-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.dashboard-card {
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

.dashboard-card:hover {
  transform: translateY(-5px);
}

/* ألوان البطاقات */
.card-blue {
  background-color: #cfe2ff;
  border-left: 5px solid #0d6efd;
}

.card-green {
  background-color: #d1e7dd;
  border-left: 5px solid #198754;
}

.card-yellow {
  background-color: #fff3cd;
  border-left: 5px solid #ffc107;
}

.card-red {
  background-color: #f8d7da;
  border-left: 5px solid #dc3545;
}

/* ألوان البطاقات في الوضع المظلم */
.dark-mode .card-blue {
  background-color: #0d6efd33;
  border-left: 5px solid #0d6efd;
}

.dark-mode .card-green {
  background-color: #19875433;
  border-left: 5px solid #198754;
}

.dark-mode .card-yellow {
  background-color: #ffc10733;
  border-left: 5px solid #ffc107;
}

.dark-mode .card-red {
  background-color: #dc354533;
  border-left: 5px solid #dc3545;
}

/* حاوية البحث */
.search-container {
  margin-bottom: 20px;
}

/* حاوية الرسوم البيانية */
.chart-container {
  height: 300px;
  margin-bottom: 20px;
}

/* شارة الإشعارات */
.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #dc3545;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

/* حاوية الإشعارات */
.notification-container {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  padding: 10px;
  border-bottom: 1px solid #dee2e6;
}

.notification-item:last-child {
  border-bottom: none;
}

/* أزرار الأيقونات */
.btn-icon {
  position: relative;
}

/* مؤشر التحميل */
.loading-spinner {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  justify-content: center;
  align-items: center;
}

.spinner-border {
  width: 3rem;
  height: 3rem;
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
  .main-container {
    margin: 20px 10px;
    padding: 15px;
  }
  
  .dashboard-cards {
    grid-template-columns: 1fr;
  }
}
