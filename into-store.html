<!-- إضافة للمخزون -->
<div class="tab-pane fade" id="intoStore">
  <h2>إضافة للمخزون</h2>
  <form id="intoStoreForm">
    <div class="row">
      <div class="col-md-6 mb-3">
        <label for="barcode" class="form-label">الباركود</label>
        <div class="input-group">
          <input type="text" class="form-control" id="barcode" placeholder="أدخل الباركود" required>
          <button class="btn btn-outline-secondary" type="button" id="scanBarcodeBtn" title="مسح الباركود">
            <i class="bi bi-upc-scan"></i>
          </button>
        </div>
      </div>
      <div class="col-md-6 mb-3">
        <label for="category" class="form-label">الفئة</label>
        <select class="form-select" id="category" required>
          <option value="">اختر الفئة</option>
        </select>
      </div>
    </div>
    <div class="row">
      <div class="col-md-4 mb-3">
        <label for="number" class="form-label">الكمية</label>
        <input type="number" class="form-control" id="number" placeholder="أدخل الكمية" required>
      </div>
      <div class="col-md-4 mb-3">
        <label for="date" class="form-label">تاريخ الإضافة</label>
        <input type="date" class="form-control" id="date" required>
      </div>
      <div class="col-md-4 mb-3">
        <label for="expiryDate" class="form-label">تاريخ انتهاء الصلاحية</label>
        <input type="date" class="form-control" id="expiryDate">
      </div>
    </div>
    <button type="submit" class="btn btn-success">حفظ</button>
    <button type="reset" class="btn btn-secondary">إعادة تعيين</button>
  </form>
  
  <div class="mt-4">
    <h3>آخر العمليات</h3>
    <div class="table-container">
      <table class="table table-bordered table-striped" id="recentIntoStoreTable">
        <thead class="table-dark">
          <tr>
            <th>الباركود</th>
            <th>الفئة</th>
            <th>الكمية</th>
            <th>التاريخ</th>
            <th>المستخدم</th>
          </tr>
        </thead>
        <tbody></tbody>
      </table>
    </div>
  </div>
</div>
