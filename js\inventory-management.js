// إدارة المخزون - إضافة وسحب العناصر

// إضافة عنصر للمخزون
function addToStore(barcode, category, quantity, date, expiryDate) {
  try {
    // التحقق من صحة البيانات
    if (!barcode || !category || !quantity || !date) {
      throw new Error('جميع الحقول مطلوبة');
    }
    
    if (quantity <= 0) {
      throw new Error('الكمية يجب أن تكون أكبر من صفر');
    }
    
    // البحث عن العنصر الموجود
    let existingItem = storeItems.find(item => item.barcode === barcode);
    
    if (existingItem) {
      // تحديث الكمية للعنصر الموجود
      existingItem.balance += parseInt(quantity);
      existingItem.lastUpdated = new Date().toISOString();
      if (expiryDate) existingItem.expiryDate = expiryDate;
    } else {
      // إضافة عنصر جديد
      const newItem = {
        id: Date.now(),
        barcode: barcode,
        category: category,
        balance: parseInt(quantity),
        expiryDate: expiryDate || null,
        lastUpdated: new Date().toISOString(),
        createdAt: new Date().toISOString()
      };
      storeItems.push(newItem);
    }
    
    // إضافة المعاملة
    const transaction = {
      id: Date.now(),
      barcode: barcode,
      category: category,
      type: 'in',
      quantity: parseInt(quantity),
      date: date,
      timestamp: new Date().toISOString(),
      user: currentUser ? currentUser.username : 'غير محدد',
      balance: existingItem ? existingItem.balance : parseInt(quantity)
    };
    transactions.push(transaction);
    
    saveData();
    showNotification('تم إضافة العنصر بنجاح', 'success');
    
    // تحديث الإحصائيات
    updateDashboard();
    checkLowStock();
    
    return true;
  } catch (error) {
    console.error('Error adding to store:', error);
    showNotification('خطأ في إضافة العنصر: ' + error.message, 'error');
    return false;
  }
}

// سحب عنصر من المخزون
function removeFromStore(barcode, quantity, date) {
  try {
    // التحقق من صحة البيانات
    if (!barcode || !quantity || !date) {
      throw new Error('جميع الحقول مطلوبة');
    }
    
    if (quantity <= 0) {
      throw new Error('الكمية يجب أن تكون أكبر من صفر');
    }
    
    // البحث عن العنصر
    let item = storeItems.find(item => item.barcode === barcode);
    
    if (!item) {
      throw new Error('العنصر غير موجود في المخزون');
    }
    
    if (item.balance < quantity) {
      throw new Error('الكمية المطلوبة أكبر من الرصيد المتاح (' + item.balance + ')');
    }
    
    // تحديث الرصيد
    item.balance -= parseInt(quantity);
    item.lastUpdated = new Date().toISOString();
    
    // إضافة المعاملة
    const transaction = {
      id: Date.now(),
      barcode: barcode,
      category: item.category,
      type: 'out',
      quantity: parseInt(quantity),
      date: date,
      timestamp: new Date().toISOString(),
      user: currentUser ? currentUser.username : 'غير محدد',
      balance: item.balance
    };
    transactions.push(transaction);
    
    saveData();
    showNotification('تم سحب العنصر بنجاح', 'success');
    
    // تحديث الإحصائيات
    updateDashboard();
    checkLowStock();
    
    return true;
  } catch (error) {
    console.error('Error removing from store:', error);
    showNotification('خطأ في سحب العنصر: ' + error.message, 'error');
    return false;
  }
}

// تحميل عناصر المخزون
function loadStoreItems(searchBarcode = '', searchCategory = '', searchDate = '') {
  const tableBody = document.querySelector('#storeTable tbody');
  if (!tableBody) return;
  
  tableBody.innerHTML = '';
  
  let filteredItems = storeItems;
  
  // تطبيق الفلاتر
  if (searchBarcode) {
    filteredItems = filteredItems.filter(item => 
      item.barcode.toLowerCase().includes(searchBarcode.toLowerCase())
    );
  }
  
  if (searchCategory) {
    filteredItems = filteredItems.filter(item => 
      item.category === searchCategory
    );
  }
  
  if (searchDate) {
    filteredItems = filteredItems.filter(item => {
      const itemDate = new Date(item.lastUpdated).toISOString().split('T')[0];
      return itemDate === searchDate;
    });
  }
  
  if (filteredItems.length === 0) {
    tableBody.innerHTML = '<tr><td colspan="6" class="text-center">لا توجد عناصر</td></tr>';
    return;
  }
  
  filteredItems.forEach(item => {
    const row = document.createElement('tr');
    
    // تحديد لون الصف حسب حالة المخزون
    if (item.balance <= settings.lowStockThreshold) {
      row.classList.add('table-warning');
    }
    
    // تحديد لون الصف حسب تاريخ انتهاء الصلاحية
    if (item.expiryDate) {
      const expiryDate = new Date(item.expiryDate);
      const today = new Date();
      const daysUntilExpiry = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));
      
      if (daysUntilExpiry <= settings.expiryWarningDays && daysUntilExpiry > 0) {
        row.classList.add('table-warning');
      } else if (daysUntilExpiry <= 0) {
        row.classList.add('table-danger');
      }
    }
    
    row.innerHTML = `
      <td>${item.barcode}</td>
      <td>${item.category}</td>
      <td>${item.balance}</td>
      <td>${item.expiryDate ? new Date(item.expiryDate).toLocaleDateString('ar-SA') : 'غير محدد'}</td>
      <td>${new Date(item.lastUpdated).toLocaleDateString('ar-SA')}</td>
      <td>
        <button class="btn btn-sm btn-info" onclick="showItemDetails('${item.barcode}')" title="التفاصيل">
          <i class="bi bi-info-circle"></i>
        </button>
        <button class="btn btn-sm btn-warning" onclick="editItem('${item.barcode}')" title="تعديل">
          <i class="bi bi-pencil"></i>
        </button>
        <button class="btn btn-sm btn-danger" onclick="deleteItem('${item.barcode}')" title="حذف">
          <i class="bi bi-trash"></i>
        </button>
      </td>
    `;
    
    tableBody.appendChild(row);
  });
}

// حذف عنصر من المخزون
function deleteItem(barcode) {
  if (confirm('هل أنت متأكد من حذف هذا العنصر؟')) {
    const index = storeItems.findIndex(item => item.barcode === barcode);
    if (index !== -1) {
      storeItems.splice(index, 1);
      saveData();
      loadStoreItems();
      updateDashboard();
      showNotification('تم حذف العنصر بنجاح', 'success');
    }
  }
}

// تعديل عنصر في المخزون
function editItem(barcode) {
  const item = storeItems.find(item => item.barcode === barcode);
  if (!item) return;
  
  // يمكن إضافة نافذة منبثقة للتعديل هنا
  const newBalance = prompt('أدخل الرصيد الجديد:', item.balance);
  if (newBalance !== null && !isNaN(newBalance) && newBalance >= 0) {
    item.balance = parseInt(newBalance);
    item.lastUpdated = new Date().toISOString();
    saveData();
    loadStoreItems();
    updateDashboard();
    showNotification('تم تحديث العنصر بنجاح', 'success');
  }
}

// عرض تفاصيل العنصر
function showItemDetails(barcode) {
  const item = storeItems.find(item => item.barcode === barcode);
  if (!item) return;
  
  // تحديث بيانات النافذة المنبثقة
  document.getElementById('detailBarcode').textContent = item.barcode;
  document.getElementById('detailCategory').textContent = item.category;
  document.getElementById('detailBalance').textContent = item.balance;
  document.getElementById('detailExpiry').textContent = item.expiryDate ? 
    new Date(item.expiryDate).toLocaleDateString('ar-SA') : 'غير محدد';
  document.getElementById('detailLastUpdated').textContent = 
    new Date(item.lastUpdated).toLocaleDateString('ar-SA');
  
  // تحميل سجل المعاملات
  loadItemTransactions(barcode);
  
  // عرض النافذة المنبثقة
  const modal = new bootstrap.Modal(document.getElementById('itemDetailsModal'));
  modal.show();
}

// تحميل سجل معاملات العنصر
function loadItemTransactions(barcode) {
  const tableBody = document.querySelector('#itemTransactionsTable tbody');
  if (!tableBody) return;
  
  tableBody.innerHTML = '';
  
  const itemTransactions = transactions.filter(t => t.barcode === barcode)
    .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
  
  if (itemTransactions.length === 0) {
    tableBody.innerHTML = '<tr><td colspan="6" class="text-center">لا توجد معاملات</td></tr>';
    return;
  }
  
  itemTransactions.forEach(transaction => {
    const row = document.createElement('tr');
    const transactionDate = new Date(transaction.timestamp);
    
    row.innerHTML = `
      <td>
        <span class="badge ${transaction.type === 'in' ? 'bg-success' : 'bg-warning'}">
          ${transaction.type === 'in' ? 'إضافة' : 'سحب'}
        </span>
      </td>
      <td>${transaction.quantity}</td>
      <td>${transactionDate.toLocaleDateString('ar-SA')}</td>
      <td>${transactionDate.toLocaleTimeString('ar-SA')}</td>
      <td>${transaction.balance}</td>
      <td>${transaction.user}</td>
    `;
    
    tableBody.appendChild(row);
  });
}

// التحقق من المخزون المنخفض
function checkLowStock() {
  const lowStockItems = storeItems.filter(item => item.balance <= settings.lowStockThreshold);
  
  if (lowStockItems.length > 0 && settings.enableNotifications) {
    showNotification(`تحذير: يوجد ${lowStockItems.length} عنصر بمخزون منخفض`, 'warning');
  }
  
  return lowStockItems;
}

// ملء الفئة تلقائياً عند إدخال الباركود
function autoFillCategory() {
  const barcodeInput = document.getElementById('outBarcode');
  const categoryInput = document.getElementById('outCategory');
  
  if (!barcodeInput || !categoryInput) return;
  
  const barcode = barcodeInput.value.trim();
  if (barcode) {
    const item = storeItems.find(item => item.barcode === barcode);
    if (item) {
      categoryInput.value = item.category;
    } else {
      categoryInput.value = '';
    }
  } else {
    categoryInput.value = '';
  }
}
