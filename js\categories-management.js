// إدارة الفئات

// تحميل الفئات في القوائم المنسدلة
function loadCategories() {
  const categorySelects = document.querySelectorAll('#category, #searchCategory, #reportCategory');
  
  categorySelects.forEach(select => {
    // الاحتفاظ بالقيمة المحددة حالياً
    const currentValue = select.value;
    
    // مسح الخيارات الموجودة (عدا الخيار الأول)
    while (select.children.length > 1) {
      select.removeChild(select.lastChild);
    }
    
    // إضافة الفئات
    categories.forEach(category => {
      const option = document.createElement('option');
      option.value = category.name;
      option.textContent = category.name;
      select.appendChild(option);
    });
    
    // استعادة القيمة المحددة
    select.value = currentValue;
  });
}

// تحميل جدول الفئات
function loadCategoriesTable() {
  const tableBody = document.querySelector('#categoriesTable tbody');
  if (!tableBody) return;
  
  tableBody.innerHTML = '';
  
  if (categories.length === 0) {
    tableBody.innerHTML = '<tr><td colspan="3" class="text-center">لا توجد فئات</td></tr>';
    return;
  }
  
  categories.forEach(category => {
    const itemCount = storeItems.filter(item => item.category === category.name).length;
    
    const row = document.createElement('tr');
    row.innerHTML = `
      <td>${category.name}</td>
      <td>${itemCount}</td>
      <td>
        <button class="btn btn-sm btn-warning" onclick="editCategory('${category.id}')" title="تعديل">
          <i class="bi bi-pencil"></i>
        </button>
        <button class="btn btn-sm btn-danger" onclick="deleteCategory('${category.id}')" title="حذف">
          <i class="bi bi-trash"></i>
        </button>
      </td>
    `;
    
    tableBody.appendChild(row);
  });
}

// إضافة فئة جديدة
function addCategory(name, description = '') {
  try {
    if (!name || name.trim() === '') {
      throw new Error('اسم الفئة مطلوب');
    }
    
    // التحقق من عدم وجود فئة بنفس الاسم
    const existingCategory = categories.find(cat => cat.name.toLowerCase() === name.toLowerCase());
    if (existingCategory) {
      throw new Error('فئة بهذا الاسم موجودة بالفعل');
    }
    
    const newCategory = {
      id: Date.now(),
      name: name.trim(),
      description: description.trim(),
      createdAt: new Date().toISOString()
    };
    
    categories.push(newCategory);
    saveData();
    
    showNotification('تم إضافة الفئة بنجاح', 'success');
    loadCategoriesTable();
    loadCategories();
    
    return true;
  } catch (error) {
    console.error('Error adding category:', error);
    showNotification('خطأ في إضافة الفئة: ' + error.message, 'error');
    return false;
  }
}

// تعديل فئة
function editCategory(categoryId) {
  const category = categories.find(cat => cat.id == categoryId);
  if (!category) return;
  
  // تحديث النافذة المنبثقة
  document.getElementById('categoryModalLabel').textContent = 'تعديل الفئة';
  document.getElementById('categoryId').value = category.id;
  document.getElementById('categoryName').value = category.name;
  document.getElementById('categoryDescription').value = category.description || '';
  
  // عرض النافذة المنبثقة
  const modal = new bootstrap.Modal(document.getElementById('categoryModal'));
  modal.show();
}

// حذف فئة
function deleteCategory(categoryId) {
  const category = categories.find(cat => cat.id == categoryId);
  if (!category) return;
  
  // التحقق من وجود عناصر تستخدم هذه الفئة
  const itemsUsingCategory = storeItems.filter(item => item.category === category.name);
  
  if (itemsUsingCategory.length > 0) {
    showNotification(`لا يمكن حذف الفئة لأنها مستخدمة في ${itemsUsingCategory.length} عنصر`, 'warning');
    return;
  }
  
  if (confirm(`هل أنت متأكد من حذف الفئة "${category.name}"؟`)) {
    const index = categories.findIndex(cat => cat.id == categoryId);
    if (index !== -1) {
      categories.splice(index, 1);
      saveData();
      loadCategoriesTable();
      loadCategories();
      showNotification('تم حذف الفئة بنجاح', 'success');
    }
  }
}

// حفظ الفئة (إضافة أو تعديل)
function saveCategory() {
  const categoryId = document.getElementById('categoryId').value;
  const name = document.getElementById('categoryName').value.trim();
  const description = document.getElementById('categoryDescription').value.trim();
  
  if (!name) {
    showNotification('اسم الفئة مطلوب', 'warning');
    return;
  }
  
  try {
    if (categoryId) {
      // تعديل فئة موجودة
      const category = categories.find(cat => cat.id == categoryId);
      if (!category) {
        throw new Error('الفئة غير موجودة');
      }
      
      // التحقق من عدم وجود فئة أخرى بنفس الاسم
      const existingCategory = categories.find(cat => 
        cat.id != categoryId && cat.name.toLowerCase() === name.toLowerCase()
      );
      if (existingCategory) {
        throw new Error('فئة بهذا الاسم موجودة بالفعل');
      }
      
      // تحديث اسم الفئة في جميع العناصر
      const oldName = category.name;
      storeItems.forEach(item => {
        if (item.category === oldName) {
          item.category = name;
        }
      });
      
      // تحديث اسم الفئة في جميع المعاملات
      transactions.forEach(transaction => {
        if (transaction.category === oldName) {
          transaction.category = name;
        }
      });
      
      category.name = name;
      category.description = description;
      category.updatedAt = new Date().toISOString();
      
      showNotification('تم تحديث الفئة بنجاح', 'success');
    } else {
      // إضافة فئة جديدة
      if (!addCategory(name, description)) {
        return;
      }
    }
    
    saveData();
    loadCategoriesTable();
    loadCategories();
    
    // إغلاق النافذة المنبثقة
    const modal = bootstrap.Modal.getInstance(document.getElementById('categoryModal'));
    modal.hide();
    
    // مسح النموذج
    document.getElementById('categoryForm').reset();
    document.getElementById('categoryId').value = '';
    
  } catch (error) {
    console.error('Error saving category:', error);
    showNotification('خطأ في حفظ الفئة: ' + error.message, 'error');
  }
}

// إعداد أحداث الفئات
function setupCategoryEvents() {
  // زر إضافة فئة جديدة
  const addCategoryBtn = document.getElementById('addCategoryBtn');
  if (addCategoryBtn) {
    addCategoryBtn.addEventListener('click', () => {
      document.getElementById('categoryModalLabel').textContent = 'إضافة فئة جديدة';
      document.getElementById('categoryForm').reset();
      document.getElementById('categoryId').value = '';
      
      const modal = new bootstrap.Modal(document.getElementById('categoryModal'));
      modal.show();
    });
  }
  
  // زر حفظ الفئة
  const saveCategoryBtn = document.getElementById('saveCategoryBtn');
  if (saveCategoryBtn) {
    saveCategoryBtn.addEventListener('click', saveCategory);
  }
  
  // نموذج الفئة
  const categoryForm = document.getElementById('categoryForm');
  if (categoryForm) {
    categoryForm.addEventListener('submit', (e) => {
      e.preventDefault();
      saveCategory();
    });
  }
}

// تحميل فئات التقارير
function loadReportCategories() {
  const reportCategorySelect = document.getElementById('reportCategory');
  if (!reportCategorySelect) return;
  
  // مسح الخيارات الموجودة (عدا الخيار الأول)
  while (reportCategorySelect.children.length > 1) {
    reportCategorySelect.removeChild(reportCategorySelect.lastChild);
  }
  
  // إضافة الفئات
  categories.forEach(category => {
    const option = document.createElement('option');
    option.value = category.name;
    option.textContent = category.name;
    reportCategorySelect.appendChild(option);
  });
}
